"use client";

import { useEffect, useState } from "react";

export default function StreamPage() {
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    // Remove any margins/padding from body
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.overflow = "hidden";
    document.documentElement.style.margin = "0";
    document.documentElement.style.padding = "0";

    return () => {
      // Cleanup when component unmounts
      document.body.style.margin = "";
      document.body.style.padding = "";
      document.body.style.overflow = "";
      document.documentElement.style.margin = "";
      document.documentElement.style.padding = "";
    };
  }, []);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <div className="w-screen h-screen bg-black relative overflow-hidden">
      {/* Video/Stream Container */}
      <div className="w-full h-full relative">
        {/* Placeholder for actual stream */}
        <div className="w-full h-full bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-red-600 flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full animate-pulse"></div>
            </div>
            <h1 className="text-2xl font-bold mb-2">LIVE STREAM</h1>
            <p className="text-gray-300">Redirected from /?source/1</p>
            <p className="text-sm text-gray-400 mt-2">No layout • Full screen experience</p>
          </div>
        </div>

        {/* You can replace above with actual video/iframe */}
        {/* 
        <iframe 
          src="your-stream-url" 
          className="w-full h-full border-0"
          allowFullScreen
        />
        */}
      </div>

      {/* Control Overlay */}
      <div className="absolute top-4 right-4 z-50 space-x-2">
        <button
          onClick={toggleFullscreen}
          className="px-3 py-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors text-sm"
        >
          {isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
        </button>
        
        <button
          onClick={() => window.history.back()}
          className="px-3 py-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors text-sm"
        >
          Back
        </button>
      </div>

      {/* Bottom Info Bar */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        <div className="flex items-center justify-between text-white">
          <div>
            <h2 className="font-semibold">Stream Title</h2>
            <p className="text-sm text-gray-300">Live • 1,234 viewers</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm">LIVE</span>
          </div>
        </div>
      </div>
    </div>
  );
}
