import { NextResponse, type NextRequest } from "next/server";
import { updateSession } from "@/lib/supabase/middleware";

export async function middleware(request: NextRequest) {
  const url = request.nextUrl;

  // Handle source redirect logic from query parameter
  // Check for URL like /?source/1
  if (url.pathname === "/" && url.search.includes("source/")) {
    // Extract source parameter from query string
    const searchParams = url.searchParams.toString();
    const sourceMatch = searchParams.match(/source\/(\w+)/);

    if (sourceMatch) {
      const sourceParam = sourceMatch[1];

      // Check if source parameter equals "1"
      if (sourceParam === "1") {
        // Redirect to another page (no layout)
        return NextResponse.redirect(new URL("/stream", request.url));
      }

      // You can add more redirect conditions here
      if (sourceParam === "2") {
        return NextResponse.redirect(new URL("/live", request.url));
      }
    }
  }

  // Handle old path-based source URLs for backward compatibility
  if (url.pathname.startsWith("/source/")) {
    const sourceParam = url.pathname.split("/source/")[1];

    if (sourceParam === "1") {
      return NextResponse.redirect(new URL("/stream", request.url));
    }

    if (sourceParam === "2") {
      return NextResponse.redirect(new URL("/live", request.url));
    }
  }

  // Handle Supabase auth session
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
