import { NextResponse, type NextRequest } from "next/server";
import { updateSession } from "@/lib/supabase/middleware";

export async function middleware(request: NextRequest) {
  const url = request.nextUrl;

  // Handle source redirect logic
  if (url.pathname.startsWith("/source/")) {
    // Extract source parameter from URL path
    const sourceParam = url.pathname.split("/source/")[1];
    
    // Check if source parameter equals "1"
    if (sourceParam === "1") {
      // Redirect to another page
      return NextResponse.redirect(new URL("/another-page", request.url));
    }
    
    // You can add more redirect conditions here
    // Example: redirect source "2" to different page
    if (sourceParam === "2") {
      return NextResponse.redirect(new URL("/special-page", request.url));
    }
    
    // You can also check query parameters
    if (url.searchParams.get("redirect") === "home") {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  // Handle Supabase auth session
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
