"use client";

import { useEffect, useState } from "react";

export default function LivePage() {
  const [viewerCount, setViewerCount] = useState(1234);

  useEffect(() => {
    // Remove any margins/padding from body
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.overflow = "hidden";
    document.documentElement.style.margin = "0";
    document.documentElement.style.padding = "0";

    // Simulate viewer count changes
    const interval = setInterval(() => {
      setViewerCount(prev => prev + Math.floor(Math.random() * 10) - 5);
    }, 3000);

    return () => {
      clearInterval(interval);
      // Cleanup when component unmounts
      document.body.style.margin = "";
      document.body.style.padding = "";
      document.body.style.overflow = "";
      document.documentElement.style.margin = "";
      document.documentElement.style.padding = "";
    };
  }, []);

  return (
    <div className="w-screen h-screen bg-black relative overflow-hidden">
      {/* Main Video Container */}
      <div className="w-full h-full relative">
        {/* Placeholder for actual live stream */}
        <div className="w-full h-full bg-gradient-to-br from-red-900 via-purple-900 to-blue-900 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="relative w-32 h-32 mx-auto mb-6">
              <div className="absolute inset-0 rounded-full bg-red-600 animate-ping opacity-75"></div>
              <div className="relative w-32 h-32 rounded-full bg-red-600 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                  <div className="w-0 h-0 border-l-4 border-l-red-600 border-t-2 border-t-transparent border-b-2 border-b-transparent ml-1"></div>
                </div>
              </div>
            </div>
            <h1 className="text-3xl font-bold mb-2">LIVE BROADCAST</h1>
            <p className="text-gray-300 mb-2">Redirected from /?source/2</p>
            <p className="text-sm text-gray-400">No layout • Immersive experience</p>
            <div className="mt-4 flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-red-400 font-semibold">ON AIR</span>
            </div>
          </div>
        </div>

        {/* You can replace above with actual video/iframe */}
        {/* 
        <iframe 
          src="your-live-stream-url" 
          className="w-full h-full border-0"
          allowFullScreen
        />
        */}
      </div>

      {/* Top Control Bar */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-4">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.history.back()}
              className="p-2 bg-black/50 rounded-lg hover:bg-black/70 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h2 className="font-semibold">Live Channel 2</h2>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">{viewerCount.toLocaleString()} viewers</span>
            </div>
            
            <button className="p-2 bg-black/50 rounded-lg hover:bg-black/70 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Chat Overlay (Optional) */}
      <div className="absolute bottom-4 right-4 w-80 h-64 bg-black/70 rounded-lg p-3 text-white text-sm overflow-hidden">
        <div className="font-semibold mb-2 border-b border-gray-600 pb-1">Live Chat</div>
        <div className="space-y-1 text-xs">
          <div><span className="text-blue-400">User123:</span> Amazing stream! 🔥</div>
          <div><span className="text-green-400">Viewer456:</span> Love this content</div>
          <div><span className="text-purple-400">Fan789:</span> Keep it up! 👏</div>
          <div><span className="text-yellow-400">Guest001:</span> How long is this live?</div>
        </div>
      </div>
    </div>
  );
}
