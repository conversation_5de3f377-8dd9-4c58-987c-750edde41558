import { createClient } from '@/lib/supabase/client';

export interface BannerRead {
  id: string;
  message_id: string;
  user_id: string;
  read_at: string;
}

export class BannerReadService {
  // Check xem banner đã được đọc chưa
  static async isBannerRead(bannerId: string, userId: string): Promise<boolean> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('message_reads')
        .select('id')
        .eq('message_id', bannerId)
        .eq('user_id', userId)
        .maybeSingle(); // Dùng maybeSingle() thay vì single()

      if (error) {
        // Error fetching banner read status
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error checking banner read status:', error);
      return false;
    }
  }

  // Mark banner as read
  static async markBannerAsRead(bannerId: string, userId: string): Promise<boolean> {
    try {
      const supabase = createClient();
      
      // Check if already exists first
      const { data: existingData, error: checkError } = await supabase
        .from('message_reads')
        .select('id')
        .eq('message_id', bannerId)
        .eq('user_id', userId)
        .maybeSingle(); // Dùng maybeSingle() thay vì single()

      if (checkError) {
        console.error('Error checking existing record:', checkError);
        return false;
      }

      if (existingData) {
        return true;
      }

      const { error } = await supabase
        .from('message_reads')
        .upsert({
          message_id: bannerId,
          user_id: userId,
          read_at: new Date().toISOString()
        }, {
          onConflict: 'message_id,user_id'
        });


      if (error) {
        // Error marking banner as read
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error marking banner as read:', error);
      return false;
    }
  }

  // Get all read banner IDs for a user
  static async getReadBannerIds(userId: string): Promise<Set<string>> {
    try {
      const supabase = createClient();
      
      const { data, error } = await supabase
        .from('message_reads')
        .select('message_id')
        .eq('user_id', userId);


      if (error) {
        console.error('Error getting read banner IDs:', error);
        return new Set();
      }

      const result = new Set(data?.map((item: { message_id: string }) => item.message_id) || []);
      return result;
    } catch (error) {
      console.error('Error getting read banner IDs:', error);
      return new Set();
    }
  }

  static async clearReadBannerIds(userId: string): Promise<boolean> {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('message_reads')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('Error clearing read banner IDs:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error clearing read banner IDs:', error);
      return false;
    }
  }

  // Test database connection and permissions
  static async testDatabaseAccess(userId: string): Promise<void> {
    try {
      const supabase = createClient();
      
      // Test 1: Check if table exists
      const { data: tableData, error: tableError } = await supabase
        .from('message_reads')
        .select('*')
        .limit(1);
      
      if (tableError) {
        console.error('❌ Table access error:', tableError);
      } else {
      }

      const testBannerId = 'test-' + Date.now();
      const { error: insertError } = await supabase
        .from('message_reads')
        .insert({
          message_id: testBannerId,
          user_id: userId,
          read_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('❌ Insert permission error:', insertError);
      } else {
        // Clean up test record
        await supabase
          .from('message_reads')
          .delete()
          .eq('message_id', testBannerId);
      }

      const { data: readData, error: readError } = await supabase
        .from('message_reads')
        .select('*')
        .eq('user_id', userId)
        .limit(5);

      if (readError) {
        console.error('❌ Read permission error:', readError);
      } else {
      }

    } catch (error) {
      console.error('❌ Database test failed:', error);
    }
  }
}
