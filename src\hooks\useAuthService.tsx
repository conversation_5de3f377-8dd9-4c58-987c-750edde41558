"use client";

import type { AuthProfile, LoginCredentials, RegisterData } from "@/types";
import { useEffect, useState } from "react";

import type { User } from "@supabase/supabase-js";
import { authService } from "@/services";

export function useAuthService() {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AuthProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { user, error } = await authService.getCurrentUser();
        if (error) {
          // Error getting current user
          setUser(null);
          setProfile(null);
          return;
        }

        setUser(user);

        if (user) {
          const { profile, error: profileError } = await authService.getProfile(
            user.id
          );
          if (profileError) {
            // Error getting profile
            setProfile(null);
            return;
          }
          setProfile(profile);
        } else {
          setProfile(null);
        }
      } catch (error) {
        // Error initializing auth
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Check if we're coming from OAuth redirect and force refresh
    const urlParams = new URLSearchParams(window.location.search);
    const hasOAuthParams =
      urlParams.has("code") ||
      urlParams.has("access_token") ||
      urlParams.has("refresh_token");

    if (hasOAuthParams) {
      // Force immediate refresh for OAuth redirects
      setTimeout(() => {
        initializeAuth();
      }, 100);
    }

    // Force refresh auth state after a short delay (useful for OAuth redirects)
    const timeoutId = setTimeout(() => {
      initializeAuth();
    }, 1000);

    const {
      data: { subscription },
    } = authService.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        const { profile, error } = await authService.getProfile(
          session.user.id
        );
        if (error) {
          // Error getting profile on auth change
          return;
        }
        setProfile(profile);
      } else {
        setProfile(null);
      }

      setLoading(false);
    });

    // Refresh auth state when window gains focus (useful after OAuth redirect)
    const handleWindowFocus = () => {
      initializeAuth();
    };

    // Refresh auth state when hash changes (useful for OAuth redirects)
    const handleHashChange = () => {
      initializeAuth();
    };

    // Refresh auth state when page becomes visible (useful for mobile)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        initializeAuth();
      }
    };

    // Refresh auth state when storage changes (useful for cross-tab sync)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "supabase.auth.token" || e.key?.startsWith("sb-")) {
        initializeAuth();
      }
    };

    window.addEventListener("focus", handleWindowFocus);
    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("storage", handleStorageChange);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearTimeout(timeoutId);
      subscription.unsubscribe();
      window.removeEventListener("focus", handleWindowFocus);
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("storage", handleStorageChange);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const signIn = async (credentials: LoginCredentials) => {
    const { user, error } = await authService.signIn(credentials);
    return { user, error };
  };

  const signUp = async (registerData: RegisterData) => {
    const { user, error } = await authService.signUp(registerData);
    return { user, error };
  };

  const signInWithGoogle = async () => {
    const { error } = await authService.signInWithGoogle();
    return { error };
  };

  const signOut = async () => {
    const { error } = await authService.signOut();
    return { error };
  };

  const updateProfile = async (
    userId: string,
    updates: Partial<AuthProfile>
  ) => {
    const { profile, error } = await authService.updateProfile(userId, updates);
    if (!error && profile) {
      setProfile(profile);
    }
    return { profile, error };
  };

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile,
  };
}
