"use client";

import type { AuthProfile, LoginCredentials, RegisterData } from "@/types";
import { useEffect, useState } from "react";

import type { User } from "@supabase/supabase-js";
import { authService } from "@/services";

export function useAuthService() {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AuthProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log("🚀 Initializing auth...");
        const { user, error } = await authService.getCurrentUser();
        if (error) {
          console.error("❌ Error getting current user:", error);
          setUser(null);
          setProfile(null);
          return;
        }

        console.log("👤 Current user:", user?.email || "No user");
        setUser(user);

        if (user) {
          console.log("📝 Getting profile for user:", user.id);
          const { profile, error: profileError } = await authService.getProfile(
            user.id
          );
          if (profileError) {
            console.error("❌ Error getting profile:", profileError);
            // If profile doesn't exist, try to create one for Google users
            if (
              profileError.message.includes("No rows returned") ||
              profileError.message.includes("PGRST116")
            ) {
              console.log("🔧 Creating profile for Google user in init...");
              try {
                await authService.createUserProfile(user.id, {
                  full_name:
                    user.user_metadata?.full_name ||
                    user.email?.split("@")[0] ||
                    "User",
                  role: "member",
                });
                // Try to get profile again after creation
                const { profile: newProfile } = await authService.getProfile(
                  user.id
                );
                console.log(
                  "✅ Profile created and loaded in init:",
                  newProfile?.full_name
                );
                setProfile(newProfile);
              } catch (createError) {
                console.error(
                  "❌ Failed to create profile in init:",
                  createError
                );
                setProfile(null);
              }
            } else {
              setProfile(null);
            }
            return;
          }
          console.log("✅ Profile loaded in init:", profile?.full_name);
          setProfile(profile);
        } else {
          console.log("🚫 No user in init, clearing profile");
          setProfile(null);
        }
      } catch (error) {
        console.error("💥 Error initializing auth:", error);
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Check if we're coming from OAuth redirect and force refresh
    const urlParams = new URLSearchParams(window.location.search);
    const hasOAuthParams =
      urlParams.has("code") ||
      urlParams.has("access_token") ||
      urlParams.has("refresh_token");

    if (hasOAuthParams) {
      // Force immediate refresh for OAuth redirects
      setTimeout(() => {
        initializeAuth();
      }, 100);
    }

    // Force refresh auth state after a short delay (useful for OAuth redirects)
    const timeoutId = setTimeout(() => {
      initializeAuth();
    }, 1000);

    const {
      data: { subscription },
    } = authService.onAuthStateChange(async (event, session) => {
      console.log("🔄 Auth state changed:", event, session?.user?.email);
      setUser(session?.user ?? null);

      if (session?.user) {
        console.log("👤 Getting profile for user:", session.user.id);
        const { profile, error } = await authService.getProfile(
          session.user.id
        );
        if (error) {
          console.error("❌ Error getting profile on auth change:", error);
          // If profile doesn't exist, try to create one for Google users
          if (
            error.message.includes("No rows returned") ||
            error.message.includes("PGRST116")
          ) {
            console.log("🔧 Creating profile for Google user...");
            try {
              await authService.createUserProfile(session.user.id, {
                full_name:
                  session.user.user_metadata?.full_name ||
                  session.user.email?.split("@")[0] ||
                  "User",
                role: "member",
              });
              // Try to get profile again after creation
              const { profile: newProfile } = await authService.getProfile(
                session.user.id
              );
              console.log(
                "✅ Profile created and loaded:",
                newProfile?.full_name
              );
              setProfile(newProfile);
            } catch (createError) {
              console.error("❌ Failed to create profile:", createError);
              setProfile(null);
            }
          } else {
            setProfile(null);
          }
          return;
        }
        console.log("✅ Profile loaded:", profile?.full_name);
        setProfile(profile);
      } else {
        console.log("🚫 No user, clearing profile");
        setProfile(null);
      }

      setLoading(false);
    });

    // Refresh auth state when window gains focus (useful after OAuth redirect)
    const handleWindowFocus = () => {
      initializeAuth();
    };

    // Refresh auth state when hash changes (useful for OAuth redirects)
    const handleHashChange = () => {
      initializeAuth();
    };

    // Refresh auth state when page becomes visible (useful for mobile)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        initializeAuth();
      }
    };

    // Refresh auth state when storage changes (useful for cross-tab sync)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "supabase.auth.token" || e.key?.startsWith("sb-")) {
        initializeAuth();
      }
    };

    window.addEventListener("focus", handleWindowFocus);
    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("storage", handleStorageChange);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearTimeout(timeoutId);
      subscription.unsubscribe();
      window.removeEventListener("focus", handleWindowFocus);
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("storage", handleStorageChange);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const signIn = async (credentials: LoginCredentials) => {
    const { user, error } = await authService.signIn(credentials);
    return { user, error };
  };

  const signUp = async (registerData: RegisterData) => {
    const { user, error } = await authService.signUp(registerData);
    return { user, error };
  };

  const signInWithGoogle = async () => {
    const { error } = await authService.signInWithGoogle();
    return { error };
  };

  const signOut = async () => {
    const { error } = await authService.signOut();
    return { error };
  };

  const updateProfile = async (
    userId: string,
    updates: Partial<AuthProfile>
  ) => {
    const { profile, error } = await authService.updateProfile(userId, updates);
    if (!error && profile) {
      setProfile(profile);
    }
    return { profile, error };
  };

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile,
  };
}
