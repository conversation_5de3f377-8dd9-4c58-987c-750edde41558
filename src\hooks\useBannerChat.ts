import { useState, useEffect } from 'react';
import { BannerReadService } from '@/services/bannerReadService';

// Interface cho banner data
export interface BannerData {
  order: string;
  type: string;
  expert_uid: string;
  expert_image: string;
  expert: string;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status: string;
  displayed: string;
  winDisplayed: string;
  updatedAt: string;
  [key: string]: string;
}

// Interface cho chat message
interface ChatMessage {
  id: string;
  content: string;
  created_at: string;
}

export function useBannerChat(messages: ChatMessage[] = [], userId?: string) {
  const [showBanner, setShowBanner] = useState(false);
  const [bannerData, setBannerData] = useState<BannerData[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [processedMessageIds, setProcessedMessageIds] = useState<Set<string>>(new Set());
  const [readBannerIds, setReadBannerIds] = useState<Set<string>>(new Set());

  // Load read banner IDs từ database khi component mount
  useEffect(() => {
    const loadReadBannerIds = async () => {
      try {
        if (userId) {
          // User đã đăng nhập - dùng database
          const readIds = await BannerReadService.getReadBannerIds(userId);
          setReadBannerIds(readIds);
        } else {
          const savedIds = localStorage.getItem('shownBannerIds');
          if (savedIds) {
            try {
              const parsedIds = JSON.parse(savedIds);
              setReadBannerIds(new Set(parsedIds));
            } catch (error) {
              console.error('Error parsing localStorage data:', error);
            }
          } else {
          }
        }
      } catch (error) {
        console.error('Error loading read banner IDs:', error);
      }
    };

    loadReadBannerIds();
  }, [userId]);

  // Mark banner as read in database or localStorage
  const markBannerAsRead = async (bannerId: string) => {
    try {
      if (userId) {
        // User đã đăng nhập - dùng database
        const success = await BannerReadService.markBannerAsRead(bannerId, userId);
        if (success) {
          setReadBannerIds(prev => new Set([...prev, bannerId]));
        } else {
        }
      } else {
        // User chưa đăng nhập - dùng localStorage
        const currentIds = Array.from(readBannerIds);
        const newIds = [...currentIds, bannerId];
        localStorage.setItem('shownBannerIds', JSON.stringify(newIds));
        setReadBannerIds(prev => new Set([...prev, bannerId]));
      }
    } catch (error) {
      console.error('Error marking banner as read:', error);
    }
  };

  // Xử lý data từ chat messages và convert thành BannerData
  useEffect(() => {
    if (messages.length === 0) {
      setBannerData([]);
      return;
    }

    const newBannerData: BannerData[] = [];

    messages.forEach((message) => {
      // Skip if already processed
      if (processedMessageIds.has(message.id)) {
        return;
      }

      if (message.content) {
        try {
          // Try to parse JSON from message content
          const parsedData = JSON.parse(message.content);
          
          // Check if it's banner data format
          if (parsedData && typeof parsedData === 'object' && parsedData.expert && parsedData.tournament) {
            const bannerId = message.id; // Dùng message.id thật thay vì parsedData.id
            
            if (readBannerIds.has(bannerId)) {
              setProcessedMessageIds(prev => new Set([...prev, message.id]));
              return;
            }

            const bannerItem: BannerData = {
              order: bannerId,
              type: 'NHẬN KÈO VIP', // Default type
              expert_uid: parsedData.id || bannerId, // Dùng parsedData.id cho expert_uid
              expert_image: '/ngoaihangtv.png', // Default image
              expert: parsedData.expert || '',
              tournament: parsedData.tournament || '',
              team_name: parsedData.team_name || '',
              closing_bet: parsedData.closing_bet || '',
              saying: parsedData.saying || '',
              status: parsedData.status || '',
              displayed: 'false',
              winDisplayed: 'false',
              updatedAt: new Date().toISOString()
            };

            newBannerData.push(bannerItem);
            
            // Mark message as processed
            setProcessedMessageIds(prev => new Set([...prev, message.id]));
          }
        } catch (error) {
          // Not JSON, ignore
        }
      }
    });

    // Add new banner data to existing data
    if (newBannerData.length > 0) {
      setBannerData(prev => [...prev, ...newBannerData]);
    }
  }, [messages, processedMessageIds, readBannerIds]);

  // Auto-rotate banner khi có nhiều banner
  useEffect(() => {
    const interval = setInterval(() => {
      if (bannerData.length > 1) {
        setCurrentBannerIndex((prev) => (prev + 1) % bannerData.length);
      }
    }, 12000); // Rotate mỗi 12 giây

    return () => clearInterval(interval);
  }, [bannerData.length]);

  // Show banner khi có data mới
  useEffect(() => {
    if (bannerData.length === 0) {
      setShowBanner(false);
      return;
    }

    const currentBanner = bannerData[currentBannerIndex];
    if (!currentBanner) {
      setShowBanner(false);
      return;
    }

    // Check if this banner has already been read by this user
    if (readBannerIds.has(currentBanner.order)) {
      setShowBanner(false);
      return;
    }

    // Double-check with database before showing banner
    const checkBannerReadStatus = async () => {
      if (!userId) {
        // No user, show banner anyway (localStorage fallback)
        setShowBanner(true);
        markBannerAsRead(currentBanner.order);
        return;
      }

      try {
        const isRead = await BannerReadService.isBannerRead(currentBanner.order, userId);
        
        if (isRead) {
          setShowBanner(false);
          // Update local state to avoid future checks
          setReadBannerIds(prev => new Set([...prev, currentBanner.order]));
          return;
        }

        setShowBanner(true);
        markBannerAsRead(currentBanner.order);
      } catch (error) {
        console.error('Error checking banner read status:', error);
        // On error, show banner anyway to avoid blocking user experience
        setShowBanner(true);
        markBannerAsRead(currentBanner.order);
      }
    };

    checkBannerReadStatus();

    // Hide banner after 6 seconds
    const hideTimer = setTimeout(() => {
      setShowBanner(false);
    }, 10000);

    return () => clearTimeout(hideTimer);
  }, [bannerData.length, currentBannerIndex, readBannerIds, userId]);

  // Function to clear read banner IDs (for testing)
  const clearReadBannerIds = async () => {
    try {
      if (userId) {
        // User đã đăng nhập - xóa từ database
        const success = await BannerReadService.clearReadBannerIds(userId);
        if (success) {
          setReadBannerIds(new Set());
        }
      } else {
        // User chưa đăng nhập - xóa từ localStorage
        localStorage.removeItem('shownBannerIds');
        setReadBannerIds(new Set());
      }
    } catch (error) {
      console.error('Error clearing read banner IDs:', error);
    }
  };

  return {
    showBanner,
    setShowBanner,
    bannerData,
    setBannerData,
    currentBannerIndex,
    setCurrentBannerIndex,
    clearReadBannerIds
  };
}
