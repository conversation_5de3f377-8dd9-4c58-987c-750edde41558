import { useState, useCallback, useEffect, useRef } from "react";
import { chatService } from "@/services/chat.service";
import type { ChatRoom, ChatMessage } from "@/types";

export function useChatService(userId: string) {
    const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [loading, setLoading] = useState(false);
    const [messagesLoading, setMessagesLoading] = useState(false);
    const [hasLoaded, setHasLoaded] = useState(false);
    const [currentRoomId, setCurrentRoomId] = useState<string | null>(null);
    const [subscription, setSubscription] = useState<(() => void) | null>(null);
    const subscriptionRef = useRef<(() => void) | null>(null);

    // Load messages for a specific room
    const loadMessages = useCallback(async (roomId: string) => {
        try {
            setMessagesLoading(true);
            const { messages, error } = await chatService.getMessages(roomId);
            if (error) {
                // Error loading messages
                return;
            }
            setMessages(messages);
            
            // Subscribe to real-time messages for this room
            
            // Unsubscribe from previous room if exists
            if (subscriptionRef.current) {
                subscriptionRef.current();
            }
            
            // Subscribe to new room
            const unsubscribe = chatService.subscribeToMessages(roomId, (newMessage: ChatMessage) => {
                setMessages(prev => [...prev, newMessage]);
            });
            
            subscriptionRef.current = unsubscribe;
            setSubscription(() => unsubscribe);
            setCurrentRoomId(roomId);
        } catch (error) {
            console.error("Error loading messages:", error);
        } finally {
            setMessagesLoading(false);
        }
    }, [currentRoomId]);

    // Load chat rooms
    const loadChatRooms = useCallback(async () => {
        if (!userId || hasLoaded) {
            return;
        }

        try {
            setLoading(true);
            const { rooms, error } = await chatService.getUserChatRooms(userId);
            if (error) {
                // Error loading chat rooms
                return;
            }
            setChatRooms(rooms);
            setHasLoaded(true);
            
            // Load messages for the first room if available
            if (rooms.length > 0) {
                await loadMessages(rooms[0].id);
            }
        } catch (error) {
            console.error("Error loading chat rooms:", error);
        } finally {
            setLoading(false);
        }
    }, [userId, hasLoaded, loadMessages]);

    // Force reload chat rooms (bypass hasLoaded check)
    const reloadChatRooms = useCallback(async () => {
        if (!userId) {
            return;
        }

        try {
            setLoading(true);
            setHasLoaded(false);
            const { rooms, error } = await chatService.getUserChatRooms(userId);
            if (error) {
                // Error reloading chat rooms
                return;
            }
            setChatRooms(rooms);
            setHasLoaded(true);
            
            // Load messages for the first room if available
            if (rooms.length > 0) {
                await loadMessages(rooms[0].id);
            }
        } catch (error) {
            console.error("Error reloading chat rooms:", error);
        } finally {
            setLoading(false);
        }
    }, [userId, loadMessages]);

    // Load messages for a specific room by name or type
    const loadMessagesForRoom = useCallback(async (roomIdentifier: string) => {
        if (!chatRooms.length) {
            return;
        }
        
        
        // Try to find room by name first, then by type
        let targetRoom = chatRooms.find(room => 
            room.name === roomIdentifier || 
            room.type === roomIdentifier ||
            room.id === roomIdentifier
        );
        
        // If not found, try to get general room
        if (!targetRoom) {
            targetRoom = chatRooms.find(room => room.type === 'general');
        }
        
        // Final fallback to first room if no general room exists
        if (!targetRoom && chatRooms.length > 0) {
            targetRoom = chatRooms[0];
        }
        
        if (targetRoom) {
            await loadMessages(targetRoom.id);
        } else {
        }
    }, [chatRooms, loadMessages]);

    // Send a message
    const sendMessage = useCallback(async (roomId: string, content: string) => {
        if (!userId) {
            return { error: new Error("User not authenticated") };
        }

        // Validate userId is a valid UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(userId)) {
            // Invalid userId (not a UUID)
            return { error: new Error("Invalid user ID") };
        }
        try {
            const { error } = await chatService.sendMessage(roomId, userId, content);
            if (error) {
                // Error sending message
                return { error };
            }
            return { error: null };
        } catch (error) {
            console.error("Error sending message:", error);
            return { error: error instanceof Error ? error : new Error("Unknown error") };
        }
    }, [userId]);

    // Create a new chat room
    const createChat = useCallback(async (name: string, type: "direct" | "group" | "private") => {
        if (!userId) return { error: new Error("User not authenticated") };

        try {
            const { data, error } = await chatService.createChat({ name, type }, userId);
            if (error) {
                // Error creating chat
                return { error };
            }
            
            // Reload chat rooms to include the new room
            setHasLoaded(false);
            await loadChatRooms();
            
            return { room: data, error: null };
        } catch (error) {
            console.error("Error creating chat:", error);
            return { error: error instanceof Error ? error : new Error("Unknown error") };
        }
    }, [userId, loadChatRooms]);

    // Delete a chat room
    const deleteChat = useCallback(async (roomId: string) => {
        try {
            const { error } = await chatService.deleteChat(roomId);
            if (error) {
                // Error deleting chat
                return { error };
            }
            
            // Reload chat rooms to remove the deleted room
            setHasLoaded(false);
            await loadChatRooms();
            
            return { error: null };
        } catch (error) {
            console.error("Error deleting chat:", error);
            return { error: error instanceof Error ? error : new Error("Unknown error") };
        }
    }, [loadChatRooms]);

    // Join a chat room
    const joinChat = useCallback(async (roomId: string) => {
        if (!userId) return { error: new Error("User not authenticated") };

        try {
            const { error } = await chatService.joinChat(roomId, userId);
            if (error) {
                // Error joining chat
                return { error };
            }
            
            // Reload chat rooms to include the joined room
            setHasLoaded(false);
            await loadChatRooms();
            
            return { error: null };
        } catch (error) {
            console.error("Error joining chat:", error);
            return { error: error instanceof Error ? error : new Error("Unknown error") };
        }
    }, [userId, loadChatRooms]);

    // Reset state when userId changes
    useEffect(() => {
        setHasLoaded(false);
        setChatRooms([]);
        setLoading(false);
        setCurrentRoomId(null);
        if (subscriptionRef.current) {
            subscriptionRef.current();
            subscriptionRef.current = null;
            setSubscription(null);
        }
    }, [userId]);

    // Cleanup subscription on unmount
    useEffect(() => {
        return () => {
            if (subscriptionRef.current) {
                subscriptionRef.current();
            }
        };
    }, []);

    return {
        chatRooms,
        messages,
        loading,
        messagesLoading,
        loadChatRooms,
        reloadChatRooms,
        loadMessages,
        loadMessagesForRoom,
        sendMessage,
        createChat,
        deleteChat,
        joinChat,
    };
}