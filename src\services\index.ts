// Auth Service
export { authService } from "./auth.service";

// Chat Service
export { chatService } from "./chat.service";

// Profile Service
export { profileService } from "./profile.service";

// Re-export types from types directory
export type {
    Profile as AuthProfile,
    LoginCredentials,
    RegisterData,
    AuthResponse,
    ProfileResponse as AuthProfileResponse,
} from "@/types/auth.types";

export type {
    ChatRoom,
    ChatMessage,
    CreateChatData,
    ChatResponse,
    MessagesResponse,
} from "@/types/chat.types";

export type {
    Profile,
    UpdateProfileData,
    ProfileResponse,
    ProfilesResponse,
} from "@/types/profile.types";
