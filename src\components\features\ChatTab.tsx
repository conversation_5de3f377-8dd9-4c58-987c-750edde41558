"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import { 
  ChatMessage as ChatMessageType, 
  subscribeToChat, 
  sendChatMessage, 
  getRandomColor,
  fetchOlderMessages,
  getChatUserCount,
  updateUserPresence
} from '@/services/chatService';
import type { ChatMessage as ChatMessageApi } from '@/types/chat.types';
import { useAuth } from '@/contexts/AuthContext';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

interface ChatTabProps {
  isLoggedIn: boolean;
  onOpenAuthModal: (mode: 'login' | 'register') => void;
  matchId?: string;
  chatType?: 'general' | 'direct';
}

export default function ChatTab({ isLoggedIn, onOpenAuthModal, matchId = 'default', chatType = 'general' }: ChatTabProps) {
  const { user, chatRooms, chatRoomsLoading, messages, messagesLoading, loadChatRooms, reloadChatRooms, loadMessagesForRoom, sendMessage } = useAuth();
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [totalMessageCount, setTotalMessageCount] = useState(0);
  const [userCount, setUserCount] = useState(0);
  const [canLoadMore, setCanLoadMore] = useState(true);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);
  const [selectedDirectRoom, setSelectedDirectRoom] = useState<string | null>(null);
  const [activeSubTab, setActiveSubTab] = useState<'chat' | 'betting'>('chat');
  
  // Mobile viewport handling
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  
  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Filter chat rooms based on chatType
  const filteredChatRooms = chatType === 'direct' 
    ? chatRooms.filter(room => room.type === 'direct')
    : chatRooms;

  // Get user data from context or localStorage as fallback
  const getUserData = useCallback(() => {
    // First try to get from context
    if (user) {
      return {
        userId: user.id,
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        email: user.email,
        verified: user.email_confirmed_at ? true : false,
        isAdmin: false // You can add admin logic here
      };
    }
    
    // Fallback to localStorage for backward compatibility
    const storedUser = localStorage.getItem('userData');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (error) {
        // Error parsing user data
      }
    }
    return null;
  }, [user]);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setViewportHeight(vh);
    setIsMobile(vw < 1024);
    
    // Detect iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) || 
                       (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);
    
    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(0, window.innerHeight - visualViewport.height);
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, []);

  const loadOlderMessages = useCallback(async () => {
    if (isLoadingMore || messages.length === 0) return;
    
    setIsLoadingMore(true);
    const oldestMessage = messages[0];
    
    try {
      // Note: This will need to be updated when we integrate with the new API
      // For now, we'll keep the local state management
      const olderMessages = await fetchOlderMessages(matchId, new Date(oldestMessage.created_at).getTime(), 20);
      if (olderMessages.length > 0) {
        if (olderMessages.length < 20) {
          setCanLoadMore(false);
        }
      } else {
        setCanLoadMore(false);
      }
    } catch (error) {
      // Error loading older messages
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, messages, matchId]);

  // Load messages for the current match/room when chat rooms are available
  useEffect(() => {
    if (isLoggedIn && user?.id && filteredChatRooms.length > 0 && !chatRoomsLoading) {
      if (chatType === 'direct') {
        // For direct chat, auto-select first room if none selected
        if (!selectedDirectRoom) {
          const firstDirectRoom = filteredChatRooms[0];
          setSelectedDirectRoom(firstDirectRoom.id);
          loadMessagesForRoom(firstDirectRoom.id);
        }
      } else {
        // For general chat, use matchId
        loadMessagesForRoom(matchId);
      }
    } else {
    }
  }, [isLoggedIn, user?.id, filteredChatRooms, chatRoomsLoading, matchId, loadMessagesForRoom, chatType, selectedDirectRoom, chatRooms]);

  // Auto-select first direct chat room when rooms are loaded
  useEffect(() => {
    if (chatType === 'direct' && filteredChatRooms.length > 0 && !selectedDirectRoom) {
      const firstDirectRoom = filteredChatRooms[0];
      setSelectedDirectRoom(firstDirectRoom.id);
    }
  }, [chatType, filteredChatRooms, selectedDirectRoom]);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();
    
    // Listen for viewport changes
    const handleResize = () => updateViewportInfo();
    const handleOrientationChange = () => {
      setTimeout(updateViewportInfo, 100); // Delay to get accurate measurements
    };
    
    // Listen for visual viewport changes (keyboard on mobile)
    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        updateViewportInfo();
      }
    };

    // iOS specific keyboard events
    const handleIOSKeyboardShow = () => {
      setIsKeyboardOpen(true);
      setTimeout(updateViewportInfo, 100);
    };
    
    const handleIOSKeyboardHide = () => {
      setIsKeyboardOpen(false);
      setTimeout(updateViewportInfo, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    // iOS keyboard events
    if (isIOS) {
      window.addEventListener('focusin', handleIOSKeyboardShow);
      window.addEventListener('focusout', handleIOSKeyboardHide);
    }
    
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      
      if (isIOS) {
        window.removeEventListener('focusin', handleIOSKeyboardShow);
        window.removeEventListener('focusout', handleIOSKeyboardHide);
      }
      
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
      }
    };
  }, [updateViewportInfo, isIOS]);

  useEffect(() => {
    const roomId = chatType === 'direct' ? selectedDirectRoom : matchId;
    if (!roomId) return;

    // Note: loadMessages is now handled by useChatService after loadChatRooms
    // We don't call loadMessages with matchId as it's not a valid UUID

    const unsubscribe = subscribeToChat(
      roomId,
      (newMessages, totalCount) => {
        // Note: This will need to be updated when we fully integrate with the new API
        // For now, we'll keep the local state management for real-time updates
        if (totalCount !== undefined) {
          setTotalMessageCount(totalCount);
        }
      },
      50
    );

    unsubscribeRef.current = unsubscribe;

    // Get user count
    getChatUserCount(roomId).then(setUserCount);

    // Update user presence
    const userData = getUserData();
    if (userData?.userId) {
      updateUserPresence(roomId, userData.userId, true);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
      // Update user presence to offline
      const userData = getUserData();
      if (userData?.userId) {
        updateUserPresence(roomId, userData.userId, false);
      }
    };
  }, [matchId, selectedDirectRoom, chatType, getUserData, isLoggedIn, user?.id]);

  // Auto-scroll to bottom when new messages arrive (chỉ scroll phần chat)
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(() => {
        // Chỉ scroll phần chat, không ảnh hưởng trang chính
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [messages.length]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 300); // Delay to ensure keyboard is fully open
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);
      
      // Check if we can load more messages
      if (scrollTop < 100 && canLoadMore && !isLoadingMore && messages.length > 0) {
        loadOlderMessages();
      }
    }
  }, [canLoadMore, isLoadingMore, messages.length, loadOlderMessages]);

  const handleSubmit = useCallback(async (messageText: string, replyToMessage?: ChatMessageType) => {
    if (!messageText.trim() || !isLoggedIn) return;

    const userData = getUserData();
    if (!userData) {
      onOpenAuthModal('login');
      return;
    }

    // Check if chat rooms are still loading
    if (chatRoomsLoading) {
      return;
    }

    // Check if we have any chat rooms
    if (chatRooms.length === 0) {
      await reloadChatRooms();
      return;
    }

    
    let currentRoom;
    if (chatType === 'direct') {
      // For direct chat, use selected room
      currentRoom = filteredChatRooms.find(room => room.id === selectedDirectRoom);
    } else {
      // For general chat, find by matchId or use first general room
      currentRoom = chatRooms.find(room => 
        room.name === matchId || 
        room.type === matchId ||
        room.id === matchId
      ) || chatRooms.find(room => room.type === 'general') || chatRooms[0];
    }

    if (!currentRoom) {
      // No room available to send message
      return;
    }

    try {
      const { error } = await sendMessage(currentRoom.id, messageText);
      if (error) {
        // Failed to send message
      } else {
        // Clear reply state after successful send
        setReplyTo(null);
      }
    } catch (error) {
      // Error sending message
    }
  }, [isLoggedIn, matchId, getUserData, onOpenAuthModal, chatRooms, chatRoomsLoading, reloadChatRooms, sendMessage, chatType, filteredChatRooms, selectedDirectRoom]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // This will be handled by ChatInput component
    }
  }, []);

  const renderMessage = useCallback((msg: ChatMessageApi) => {
    const userData = getUserData();
    const isOwnMessage = userData?.userId === msg.user_id;
    
    // Convert ChatMessageApi to ChatMessageType format for ChatMessage component
    const messageForComponent: ChatMessageType = {
      id: msg.id,
      message: msg.content, // Note: ChatMessageType uses 'message' not 'content'
      timestamp: new Date(msg.created_at).getTime(),
      userId: msg.user_id,
      userName: isOwnMessage ? 'Bạn' : (msg.user?.full_name || 'Unknown User'),
      userAvatar: msg.user?.avatar_url || '',
      userColor: getRandomColor(),
      verified: false, // You can add this to the API response
      isAdmin: false, // You can add this to the API response
      replyTo: null, // You can add this to the API response
      reactions: {} as { [key: string]: number }, // You can add this to the API response
      pinned: false, // You can add this to the API response
    };
    
    return (
      <ChatMessage
        key={msg.id}
        message={messageForComponent}
        isOwnMessage={false} // Always false to show all messages on the left
        onReply={(replyMessage: ChatMessageType) => {
          setReplyTo(replyMessage);
        }}
        onReact={(messageId: string, reactionType: string) => {
          // Handle reaction - you can implement this later
        }}
        onPin={(messageId: string, pinned: boolean) => {
          // Handle pin - you can implement this later
        }}
        onDelete={(messageId: string) => {
          // Handle delete - you can implement this later
        }}
        isAdmin={userData?.isAdmin || false}
      />
    );
  }, [getUserData]);

  // Calculate dynamic styles for mobile
  const getChatContainerStyle = () => {
    if (isMobile && viewportHeight > 0) {
      const inputHeight = 60; // Approximate ChatInput height
      const safeAreaBottom = 20; // Safe area bottom
      
      let availableHeight;
      if (isIOS && isKeyboardOpen) {
        // iOS: Use visual viewport height when keyboard is open
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          availableHeight = visualViewport.height - inputHeight - safeAreaBottom;
        } else {
          availableHeight = viewportHeight - inputHeight - safeAreaBottom;
        }
      } else {
        // Android or iOS without keyboard
        availableHeight = viewportHeight - inputHeight - safeAreaBottom - keyboardHeight;
      }
      
      return {
        height: `${Math.max(200, availableHeight)}px`,
        maxHeight: `${availableHeight}px`,
        paddingBottom: '20px',
        // Ensure scroll works on mobile
        overflowY: 'auto' as const,
        WebkitOverflowScrolling: 'touch' as const,
        overflowX: 'hidden' as const,
        position: 'relative' as const,
        flex: '1 1 auto'
      };
    }
    // Desktop: Ensure proper height and scroll
    return {
      height: '100%',
      paddingBottom: '20px',
      overflowY: 'auto' as const
    };
  };

  const getChatInputStyle = () => {
    if (isMobile && viewportHeight > 0) {
      let bottomPosition = '0px';
      
      if (isIOS && isKeyboardOpen) {
        // iOS: Position above keyboard using visual viewport
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          bottomPosition = `${window.innerHeight - visualViewport.height}px`;
        }
      } else if (keyboardHeight > 0) {
        // Android: Use keyboard height
        bottomPosition = `${keyboardHeight}px`;
      }
      
      return {
        position: 'fixed' as const,
        bottom: bottomPosition,
        left: '0',
        right: '0',
        zIndex: 50,
        borderTop: '1px solid var(--chat-input-border, #e5e7eb)',
        paddingBottom: 'env(safe-area-inset-bottom, 10px)',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
      };
    }
    return {};
  };

  // Handle room selection for direct chat
  const handleRoomSelect = (roomId: string) => {
    setSelectedDirectRoom(roomId);
    loadMessagesForRoom(roomId);
  };

  return (
    <div 
      className="flex flex-col h-full relative" 
      style={{ 
        paddingBottom: isMobile ? '0px' : 'env(safe-area-inset-bottom, 0px)',
        // Mobile: Ensure proper height and allow scrolling
        ...(isMobile ? {
          position: 'relative',
          height: '100%',
          overflow: 'hidden' // Let child container handle scrolling
        } : {})
      }}
    >
      {/* Sub-tabs for Direct Chat */}
      {chatType === 'direct' && (
        <div className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex">
            <button
              onClick={() => setActiveSubTab('chat')}
              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
                activeSubTab === 'chat'
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Chat riêng
            </button>
            <button
              onClick={() => setActiveSubTab('betting')}
              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
                activeSubTab === 'betting'
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Soi kèo
            </button>
          </div>
        </div>
      )}

      {/* Avatar List for Direct Chat */}
      {chatType === 'direct' && activeSubTab === 'chat' && filteredChatRooms.length > 0 && (
        <div className="p-2 border-b border-gray-200 dark:border-gray-700">
          <div className="flex gap-1.5 sm:gap-2 overflow-x-auto pb-1 scrollbar-hide">
            {filteredChatRooms.map((room) => {
              const otherUser = room.other_user;
              const isSelected = selectedDirectRoom === room.id;
              
              return (
                <div
                  key={room.id}
                  onClick={() => handleRoomSelect(room.id)}
                  className={`flex-shrink-0 flex flex-col items-center cursor-pointer transition-all duration-200 ${
                    isSelected 
                      ? 'transform scale-105' 
                      : 'hover:transform hover:scale-105'
                  }`}
                >
                  <div className={`relative w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2 transition-colors ${
                    isSelected 
                      ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
                  }`}>
                    {otherUser?.avatar_url ? (
                      <img
                        src={otherUser.avatar_url}
                        alt={otherUser.full_name || 'User'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                        {(otherUser?.full_name || room.name || 'U').charAt(0).toUpperCase()}
                      </div>
                    )}
                    {/* Online indicator */}
                    <div className="absolute bottom-0 right-0 w-2 h-2 sm:w-2.5 sm:h-2.5 bg-green-500 border border-white dark:border-gray-800 rounded-full"></div>
                    {/* Unread message count badge */}
                    {room.unread_count && room.unread_count > 0 && (
                      <div className="absolute -top-0.5 -right-0.5 w-4 h-4 sm:w-4.5 sm:h-4.5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {room.unread_count > 99 ? '99+' : room.unread_count}
                      </div>
                    )}
                  </div>
                  <div className="mt-0.5 text-center max-w-10 sm:max-w-12">
                    <p className={`text-xs truncate ${
                      isSelected 
                        ? 'text-blue-600 dark:text-blue-400 font-medium' 
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {otherUser?.full_name || room.name || 'User'}
                    </p>
                    {isSelected && (
                      <div className="w-0.5 h-0.5 bg-blue-500 rounded-full mx-auto mt-0.5"></div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Soi kèo Content */}
      {chatType === 'direct' && activeSubTab === 'betting' && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Soi kèo chuyên nghiệp</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Nhận tư vấn kèo từ các chuyên gia hàng đầu
            </p>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">A</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">Admin Tuấn Kiệt</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Chuyên gia soi kèo</p>
                  </div>
                </div>
                <button className="px-3 py-1 bg-blue-500 text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                  Liên hệ
                </button>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">B</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">Admin Minh</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Chuyên gia phân tích</p>
                  </div>
                </div>
                <button className="px-3 py-1 bg-blue-500 text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                  Liên hệ
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chat Messages */}
      {!(chatType === 'direct' && activeSubTab === 'betting') && (
        <div
          className="flex-1 overflow-y-auto p-4 relative"
          ref={chatContainerRef}
          onScroll={handleScroll}
          style={getChatContainerStyle()}
        >
        {/* Loading indicator for older messages */}
        {isLoadingMore && (
          <div className="text-center py-2">
            <div className="inline-flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span>Đang tải tin nhắn cũ...</span>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className={`space-y-2 ${isMobile ? 'pb-36' : ''}`}>
          {messages.map(renderMessage)}
        </div>


        {/* Login to comment card */}
        {!isLoggedIn && (
          <div
            className="mt-4 p-4 bg-white dark:bg-custom-dark border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm cursor-pointer relative z-20 hover:bg-white dark:hover:bg-gray-700 transition-colors"
            onClick={() => onOpenAuthModal('login')}
            style={{
              // Ensure it's always visible on mobile
              ...(isMobile ? {
                position: 'relative',
                zIndex: 20,
                marginBottom: '80px' // Add space for ChatInput
              } : {})
            }}
          >
            <div className="text-center">
              <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">Đăng nhập để bình luận</p>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
        </div>
      )}

      {/* Chat Input */}
      {!(chatType === 'direct' && activeSubTab === 'betting') && (
        <div 
          className={isMobile ? "fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-custom-dark border-t border-gray-200 dark:border-gray-700" : "mb-5 md:mb-0"}
          style={getChatInputStyle()}
        >
        <ChatInput
          onSubmit={handleSubmit}
          isLoggedIn={isLoggedIn}
          replyTo={replyTo}
          onCancelReply={() => setReplyTo(null)}
          placeholder={chatType === 'direct' ? "Nhập tin nhắn riêng..." : "Nhập tin nhắn..."}
          disabled={false}
          noBorderRadius={isMobile}
          onOpenAuthModal={onOpenAuthModal}
        />
        </div>
      )}
      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <div 
          className="absolute right-4 z-20"
          style={{
            bottom: isMobile ? '80px' : '70px'
          }}
        >
          <button
            onClick={scrollToBottom}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600 transition-all duration-200 hover:scale-105"
            title="Scroll xuống tin nhắn mới nhất"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}