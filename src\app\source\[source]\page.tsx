export default function SourcePage({
  params,
  searchParams,
}: {
  params: { source: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const { source } = params;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-lg mx-auto text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Source Page
        </h1>

        <div className="space-y-4 text-left">
          <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
              Source Parameter:
            </p>
            <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {source}
            </p>
          </div>

          {Object.keys(searchParams).length > 0 && (
            <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">
                Query Parameters:
              </p>
              {Object.entries(searchParams).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-sm text-gray-500">{key}:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {Array.isArray(value) ? value.join(", ") : value}
                  </span>
                </div>
              ))}
            </div>
          )}

          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              💡 <strong>Middleware Info:</strong>
              <br />
              • /source/1 → redirects to /another-page
              <br />
              • /source/2 → redirects to /special-page
              <br />• Other sources → show this page
            </p>
          </div>
        </div>

        <div className="mt-6 space-x-2">
          <a
            href="/"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Về trang chủ
          </a>
          <a
            href="/source/1"
            className="inline-block px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
          >
            Test /source/1
          </a>
          <a
            href="/source/2"
            className="inline-block px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
          >
            Test /source/2
          </a>
        </div>
      </div>
    </div>
  );
}
