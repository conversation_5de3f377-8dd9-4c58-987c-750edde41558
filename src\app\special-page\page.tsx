export default function SpecialPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900">
      <div className="max-w-md mx-auto text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl">
        <h1 className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-4">
          Special Page
        </h1>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Bạn đã được redirect từ /source/2 đến trang đặc biệt này!
        </p>
        <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
          <p>✨ Middleware redirect với source = "2"</p>
          <p>🔄 URL /source/2 → /special-page</p>
        </div>
        <div className="mt-6 space-x-2">
          <a 
            href="/" 
            className="inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Về trang chủ
          </a>
          <a 
            href="/another-page" 
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Another Page
          </a>
        </div>
      </div>
    </div>
  );
}
