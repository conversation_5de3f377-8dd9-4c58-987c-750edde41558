import { createClient } from "@/lib/supabase/client";
import type {
    <PERSON>t<PERSON>oom,
    ChatMessage,
    CreateChatData,
    ChatResponse,
    MessagesResponse,
} from "@/types/chat.types";

class ChatService {
    private supabase = createClient();

    async getUserChatRooms(
        userId: string
    ): Promise<{ rooms: ChatRoom[]; error: Error | null }> {
        try {
            const { data, error } = await this.supabase.rpc(
                "get_user_rooms_with_messages",
                {
                    user_uuid: userId,
                }
            );

            if (error) {
                return {
                    rooms: [],
                    error: new Error(error.message),
                };
            }

            const rooms: ChatRoom[] = data.map((room: Record<string, unknown>) => {
                const messages = room.messages as Array<{ content: string; created_at: string }> | undefined;
                return {
                    id: room.id as string,
                    name: room.name as string,
                    type: room.type as "direct" | "group" | "general" | "private",
                    last_message:
                        messages && messages.length > 0
                            ? messages[messages.length - 1].content
                            : undefined,
                    last_message_at:
                        messages && messages.length > 0
                            ? messages[messages.length - 1].created_at
                            : undefined,
                    other_user:
                        room.type === "direct"
                            ? {
                                id: room.other_user_id as string,
                                full_name: room.other_user_name as string,
                                avatar_url: room.other_user_avatar as string | undefined,
                            }
                            : undefined,
                };
            });

            return {
                rooms,
                error: null,
            };
        } catch (error) {
            return {
                rooms: [],
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async getMessages(roomId: string): Promise<MessagesResponse> {
        try {
            const { data, error } = await this.supabase
                .from("messages")
                .select(
                    `
                    id,
                    content,
                    created_at,
                    sender_id,
                    profiles!inner(full_name, avatar_url)
                `
                )
                .eq("room_id", roomId)
                .order("created_at", { ascending: true });

            if (error) {
                return {
                    messages: [],
                    error: new Error(error.message),
                };
            }

            const messages: ChatMessage[] = data.map((msg: Record<string, unknown>) => {
                const profiles = msg.profiles as { full_name: string; avatar_url?: string };
                return {
                    id: msg.id as string,
                    content: msg.content as string,
                    created_at: msg.created_at as string,
                    user_id: msg.sender_id as string,
                    user: {
                        full_name: profiles.full_name,
                        avatar_url: profiles.avatar_url,
                    },
                };
            });

            return {
                messages,
                error: null,
            };
        } catch (error) {
            return {
                messages: [],
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async sendMessage(
        roomId: string,
        senderId: string,
        content: string
    ): Promise<{ error: Error | null }> {
        try {
            const { error } = await this.supabase.from("messages").insert({
                content: content.trim(),
                room_id: roomId,
                sender_id: senderId,
            });

            return {
                error: error ? new Error(error.message) : null,
            };
        } catch (error) {
            return {
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async createChat(
        chatData: CreateChatData,
        creatorId: string
    ): Promise<ChatResponse> {
        try {
            // Create chat room
            const { data: roomData, error: roomError } = await this.supabase
                .from("chat_rooms")
                .insert({
                    name: chatData.name,
                    type: chatData.type,
                    created_by: creatorId,
                })
                .select()
                .single();

            if (roomError) {
                return {
                    data: null,
                    error: new Error(roomError.message),
                };
            }

            // Add creator as member
            const { error: memberError } = await this.supabase
                .from("chat_room_members")
                .insert({
                    room_id: roomData.id,
                    user_id: creatorId,
                });

            if (memberError) {
                return {
                    data: null,
                    error: new Error(memberError.message),
                };
            }

            // Add other members if provided
            if (chatData.members && chatData.members.length > 0) {
                const memberInserts = chatData.members.map(memberId => ({
                    room_id: roomData.id,
                    user_id: memberId,
                }));

                const { error: membersError } = await this.supabase
                    .from("chat_room_members")
                    .insert(memberInserts);

                if (membersError) {
                    return {
                        data: null,
                        error: new Error(membersError.message),
                    };
                }
            }

            return {
                data: roomData,
                error: null,
            };
        } catch (error) {
            return {
                data: null,
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async deleteChat(roomId: string): Promise<{ error: Error | null }> {
        try {
            const { error } = await this.supabase
                .from("chat_rooms")
                .delete()
                .eq("id", roomId);

            return {
                error: error ? new Error(error.message) : null,
            };
        } catch (error) {
            return {
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async joinChat(
        roomId: string,
        userId: string
    ): Promise<{ error: Error | null }> {
        try {
            const { error } = await this.supabase.from("chat_room_members").insert({
                room_id: roomId,
                user_id: userId,
            });

            return {
                error: error ? new Error(error.message) : null,
            };
        } catch (error) {
            return {
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    async getChatRoom(
        roomId: string
    ): Promise<{ room: ChatRoom | null; error: Error | null }> {
        try {
            const { data, error } = await this.supabase
                .from("chat_rooms")
                .select("*")
                .eq("id", roomId)
                .single();

            return {
                room: data,
                error: error ? new Error(error.message) : null,
            };
        } catch (error) {
            return {
                room: null,
                error: error instanceof Error ? error : new Error("Unknown error"),
            };
        }
    }

    subscribeToMessages(
        roomId: string,
        callback: (message: ChatMessage) => void
    ) {
        const subscription = this.supabase
            .channel(`messages:${roomId}`)
            .on(
                "postgres_changes",
                {
                    event: "INSERT",
                    schema: "public",
                    table: "messages",
                    filter: `room_id=eq.${roomId}`,
                },
                async payload => {
                    const newMessage = payload.new;

                    // Fetch user profile for the new message
                    const { data: profileData } = await this.supabase
                        .from("profiles")
                        .select("full_name, avatar_url")
                        .eq("id", newMessage.sender_id)
                        .single();

                    const formattedMessage: ChatMessage = {
                        id: newMessage.id,
                        content: newMessage.content,
                        created_at: newMessage.created_at,
                        user_id: newMessage.sender_id,
                        user: {
                            full_name: profileData?.full_name || "Unknown User",
                            avatar_url: profileData?.avatar_url,
                        },
                    };

                    callback(formattedMessage);
                }
            )
            .subscribe();

        return () => {
            subscription.unsubscribe();
        };
    }
}

export const chatService = new ChatService();
